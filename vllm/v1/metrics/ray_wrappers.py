# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project
import time
from typing import Optional, Union

from vllm.v1.metrics.loggers import PrometheusStatLogger
from vllm.v1.spec_decode.metrics import SpecDecoding<PERSON>rom

try:
    from ray import serve
    from ray.serve.context import ReplicaContext
    from ray.util import metrics as ray_metrics
    from ray.util.metrics import Metric
except ImportError:
    ray_metrics = None


def _get_replica_id() -> str:
    ctx: ReplicaContext = serve.get_replica_context()
    return ctx.replica_id.unique_id


class RayPrometheusMetric:

    def __init__(self):
        if ray_metrics is None:
            raise ImportError(
                "RayPrometheusMetric requires Ray to be installed.")

        self.metric: Metric = None
        self.replica_id = _get_replica_id()

    def _setup_metric_with_replica_id(self, metric_factory, name, documentation, labelnames, **kwargs):
        """
        Centralized method to create any Ray metric with ReplicaId automatically added.

        Args:
            metric_factory: The Ray metric class (e.g., ray_metrics.Gauge)
            name: Metric name
            documentation: Metric description
            labelnames: Original labelnames from user
            **kwargs: Additional arguments for the metric (e.g., boundaries for Histogram)
        """
        # Add ReplicaId to tag_keys to allow setting it as a default tag
        labelnames_list = list(labelnames) if labelnames else []
        if "ReplicaId" not in labelnames_list:
            labelnames_list.append("ReplicaId")
        labelnames_tuple = tuple(labelnames_list)

        # Create the metric with ReplicaId included in tag_keys
        self.metric = metric_factory(
            name=name,
            description=documentation,
            tag_keys=labelnames_tuple,
            **kwargs
        )

        # Set default tags for ALL tag keys to avoid "Missing value" errors
        initial_tags = {"ReplicaId": self.replica_id}
        for tag_key in labelnames_tuple:
            if tag_key != "ReplicaId":
                initial_tags[tag_key] = ""

        self.metric.set_default_tags(initial_tags)

    def labels(self, *labels, **labelskwargs):
        # Build the final tags dictionary, always including ReplicaId
        final_tags = {"ReplicaId": self.replica_id}

        # Ensure all tag keys have values (provide empty defaults for missing ones)
        for tag_key in self.metric._tag_keys:
            if tag_key not in final_tags:
                final_tags[tag_key] = ""

        if labelskwargs:
            for k, v in labelskwargs.items():
                if not isinstance(v, str):
                    labelskwargs[k] = str(v)
            final_tags.update(labelskwargs)

        if labels:
            tag_keys = list(self.metric._tag_keys)
            # Check against original tag keys (without ReplicaId) for backward compatibility
            original_tag_keys = [key for key in tag_keys if key != "ReplicaId"]
            if len(labels) != len(original_tag_keys):
                raise ValueError(
                    "Number of labels must match the number of original tag keys. "
                    f"Expected {len(original_tag_keys)}, got {len(labels)}"
                )

            label_dict = dict(zip(original_tag_keys, labels))
            final_tags.update(label_dict)

        # Ensure ReplicaId is always preserved
        final_tags["ReplicaId"] = self.replica_id

        self.metric.set_default_tags(final_tags)
        return self


class RayGaugeWrapper(RayPrometheusMetric):
    """Wraps around ray.util.metrics.Gauge to provide same API as
    prometheus_client.Gauge"""

    def __init__(self,
                 name: str,
                 documentation: Optional[str] = "",
                 labelnames: Optional[list[str]] = None,
                 multiprocess_mode: Optional[str] = ""):
        super().__init__()

        # All Ray metrics are keyed by WorkerId, so multiprocess modes like
        # "mostrecent", "all", "sum" do not apply. This logic can be manually
        # implemented at the observability layer (Prometheus/Grafana).
        del multiprocess_mode

        # Use centralized method to create metric with ReplicaId
        self._setup_metric_with_replica_id(
            ray_metrics.Gauge, name, documentation, labelnames
        )

    def set(self, value: Union[int, float]):
        return self.metric.set(value)

    def set_to_current_time(self):
        # ray metrics doesn't have set_to_current time, https://docs.ray.io/en/latest/_modules/ray/util/metrics.html
        return self.metric.set(time.time())


class RayCounterWrapper(RayPrometheusMetric):
    """Wraps around ray.util.metrics.Counter to provide same API as
    prometheus_client.Counter"""

    def __init__(self,
                 name: str,
                 documentation: Optional[str] = "",
                 labelnames: Optional[list[str]] = None):
        super().__init__()

        # Use centralized method to create metric with ReplicaId
        self._setup_metric_with_replica_id(
            ray_metrics.Counter, name, documentation, labelnames
        )

    def inc(self, value: Union[int, float] = 1.0):
        if value == 0:
            return
        return self.metric.inc(value)


class RayHistogramWrapper(RayPrometheusMetric):
    """Wraps around ray.util.metrics.Histogram to provide same API as
    prometheus_client.Histogram"""

    def __init__(self,
                 name: str,
                 documentation: Optional[str] = "",
                 labelnames: Optional[list[str]] = None,
                 buckets: Optional[list[float]] = None):
        super().__init__()

        # Use centralized method to create metric with ReplicaId
        boundaries = buckets if buckets else []
        self._setup_metric_with_replica_id(
            ray_metrics.Histogram, name, documentation, labelnames,
            boundaries=boundaries
        )

    def observe(self, value: Union[int, float]):
        return self.metric.observe(value)


class RaySpecDecodingProm(SpecDecodingProm):
    """
    RaySpecDecodingProm is used by RayMetrics to log to Ray metrics.
    Provides the same metrics as SpecDecodingProm but uses Ray's
    util.metrics library.
    """

    _counter_cls = RayCounterWrapper


class RayPrometheusStatLogger(PrometheusStatLogger):
    """RayPrometheusStatLogger uses Ray metrics instead."""

    _gauge_cls = RayGaugeWrapper
    _counter_cls = RayCounterWrapper
    _histogram_cls = RayHistogramWrapper
    _spec_decoding_cls = RaySpecDecodingProm

    @staticmethod
    def _unregister_vllm_metrics():
        # No-op on purpose
        pass
