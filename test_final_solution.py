#!/usr/bin/env python3
"""
Final comprehensive test to verify the complete unified ReplicaId solution.
This tests the actual vLLM implementation to ensure:
1. The original error is completely fixed
2. Backward compatibility is maintained
3. ReplicaId is properly added to all metrics
4. Positional labels() calls work correctly
"""

import sys
import os

def test_actual_vllm_implementation():
    """Test the actual vLLM Ray wrapper implementation."""
    print("Testing actual vLLM Ray wrapper implementation...")
    
    try:
        import ray
        print("✓ Ray is available")
        
        # Initialize Ray
        if not ray.is_initialized():
            try:
                ray.shutdown()
            except:
                pass
            ray.init(local_mode=True, ignore_reinit_error=True)
            print("✓ Ray initialized")
        
        # Test v1 implementation
        print("\n--- Testing v1 Implementation ---")
        
        try:
            # Import the actual v1 wrappers
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'vllm', 'v1', 'metrics'))
            import ray_wrappers
            
            # Test RayGaugeWrapper
            gauge = ray_wrappers.RayGaugeWrapper("test_v1_gauge", "Test gauge", labelnames=["model", "engine"])
            print("✓ v1 RayGaugeWrapper created successfully")
            
            # Test without labels() call - should work now
            gauge.set(42)
            print("✓ v1 Gauge set() works without labels() call")
            
            # Test with positional labels() call - the critical case
            gauge.labels("test_model", "engine_0").set(100)
            print("✓ v1 Gauge positional labels() call works")
            
            # Test with keyword labels() call
            gauge.labels(model="test_model2", engine="engine_1").set(200)
            print("✓ v1 Gauge keyword labels() call works")
            
            # Test RayCounterWrapper
            counter = ray_wrappers.RayCounterWrapper("test_v1_counter", "Test counter", labelnames=["model"])
            counter.inc(1)
            print("✓ v1 RayCounterWrapper works without labels() call")
            
            counter.labels("test_model").inc(2)
            print("✓ v1 Counter positional labels() call works")
            
            # Test RayHistogramWrapper
            histogram = ray_wrappers.RayHistogramWrapper("test_v1_histogram", "Test histogram", 
                                                        labelnames=["model"], buckets=[0.1, 0.5, 1.0, 2.0])
            histogram.observe(1.5)
            print("✓ v1 RayHistogramWrapper works without labels() call")
            
            histogram.labels("test_model").observe(0.8)
            print("✓ v1 Histogram positional labels() call works")
            
            # Verify ReplicaId is in tag_keys
            if "ReplicaId" in gauge.metric._tag_keys:
                print("✓ v1 ReplicaId is in tag_keys")
            else:
                print("✗ v1 ReplicaId missing from tag_keys")
            
            sys.path.pop(0)
            
        except Exception as e:
            print(f"✗ v1 implementation test failed: {e}")
            if len(sys.path) > 0 and 'ray_wrappers' in sys.path[0]:
                sys.path.pop(0)
        
        # Test legacy implementation
        print("\n--- Testing Legacy Implementation ---")
        
        try:
            # Import the actual legacy wrappers
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'vllm', 'engine'))
            import metrics
            
            # Test _RayGaugeWrapper
            legacy_gauge = metrics._RayGaugeWrapper("test_legacy_gauge", "Test gauge", labelnames=["model", "engine"])
            print("✓ Legacy _RayGaugeWrapper created successfully")
            
            # Test without labels() call - should work now
            legacy_gauge.set(42)
            print("✓ Legacy Gauge set() works without labels() call")
            
            # Test with keyword labels() call (legacy only supports keyword)
            legacy_gauge.labels(model="test_model", engine="engine_0").set(100)
            print("✓ Legacy Gauge keyword labels() call works")
            
            # Test _RayCounterWrapper
            legacy_counter = metrics._RayCounterWrapper("test_legacy_counter", "Test counter", labelnames=["model"])
            legacy_counter.inc(1)
            print("✓ Legacy _RayCounterWrapper works without labels() call")
            
            legacy_counter.labels(model="test_model").inc(2)
            print("✓ Legacy Counter keyword labels() call works")
            
            # Test _RayHistogramWrapper
            legacy_histogram = metrics._RayHistogramWrapper("test_legacy_histogram", "Test histogram", 
                                                           labelnames=["model"], buckets=[0.1, 0.5, 1.0, 2.0])
            legacy_histogram.observe(1.5)
            print("✓ Legacy _RayHistogramWrapper works without labels() call")
            
            legacy_histogram.labels(model="test_model").observe(0.8)
            print("✓ Legacy Histogram keyword labels() call works")
            
            # Verify ReplicaId is in tag_keys
            if "ReplicaId" in legacy_gauge._gauge._tag_keys:
                print("✓ Legacy ReplicaId is in tag_keys")
            else:
                print("✗ Legacy ReplicaId missing from tag_keys")
            
            sys.path.pop(0)
            
        except Exception as e:
            print(f"✗ Legacy implementation test failed: {e}")
            if len(sys.path) > 0 and 'metrics' in sys.path[0]:
                sys.path.pop(0)
        
        # Test edge cases
        print("\n--- Testing Edge Cases ---")
        
        # Test with no labelnames
        simple_gauge = ray_wrappers.RayGaugeWrapper("simple_gauge", "Simple gauge")
        simple_gauge.set(999)
        print("✓ Simple gauge (no labelnames) works")
        
        # Test with many labelnames
        complex_gauge = ray_wrappers.RayGaugeWrapper("complex_gauge", "Complex gauge", 
                                                    labelnames=["model", "engine", "user", "endpoint"])
        complex_gauge.set(1000)
        print("✓ Complex gauge (many labelnames) works without labels() call")
        
        complex_gauge.labels("m1", "e1", "u1", "ep1").set(2000)
        print("✓ Complex gauge positional labels() call works")
        
        ray.shutdown()
        print("✓ Ray shutdown successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        try:
            ray.shutdown()
        except:
            pass
        return False

if __name__ == "__main__":
    print("Final Comprehensive ReplicaId Solution Test")
    print("=" * 70)
    print("This test verifies the complete unified solution:")
    print("1. Original ValueError is completely fixed")
    print("2. Both v1 and legacy implementations work")
    print("3. Backward compatibility is maintained")
    print("4. ReplicaId is automatically added to all metrics")
    print("5. All types of labels() calls work correctly")
    print("=" * 70)
    
    success = test_actual_vllm_implementation()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 ALL TESTS PASSED! The unified ReplicaId solution is working perfectly!")
        print("\n✅ SOLUTION SUMMARY:")
        print("  • Original error 'Missing value for tag key(s): ReplicaId' is FIXED")
        print("  • ReplicaId automatically added to all Ray metrics")
        print("  • Backward compatibility maintained for existing code")
        print("  • Positional labels() calls work correctly")
        print("  • Centralized logic in base classes")
        print("  • Both v1 and legacy implementations updated")
        print("\n🚀 Ready for production deployment!")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    print("=" * 70)
