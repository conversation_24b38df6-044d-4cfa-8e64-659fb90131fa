import os

import huggingface_hub
from huggingface_hub import snapshot_download
from modelscope import snapshot_download as modelscope_snapshot_download
from modelscope.hub.file_download import create_temporary_directory_and_cache

common_kwargs = {
        "local_files_only": True,
        "revision": None,
}
# cache = huggingface_hub.try_to_load_from_cache(repo_id="facebook/opt-125m",filename="config.json")
# print(cache)
# model_path=snapshot_download(repo_id="facebook/opt-125m",**common_kwargs)
# print(model_path)

storage_folder = os.path.join(huggingface_hub.constants.HF_HUB_CACHE, huggingface_hub.file_download.repo_folder_name(repo_id="facebook/opt-125m", repo_type="model"))
print(storage_folder)

model_path=modelscope_snapshot_download(repo_id="facebook/opt-125m",**common_kwargs)
print(model_path)

temporary_cache_dir, cache =create_temporary_directory_and_cache(model_id="facebook/opt-125m", repo_type="model")
print(cache.get_root_location())
